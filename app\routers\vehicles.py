from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app import crud, models, schemas
from app.database import get_db

router = APIRouter(
    prefix="/vehicles",
    tags=["vehicles"],
    responses={404: {"description": "Not found"}},
)

@router.post("/", response_model=schemas.Vehicle)
def create_vehicle(
    vehicle: schemas.VehicleCreate,
    db: Session = Depends(get_db)
):
    """Create a new vehicle"""
    db_vehicle = crud.get_vehicle_by_ser_no(db, ser_no=vehicle.ser_no)
    if db_vehicle:
        raise HTTPException(status_code=400, detail="Vehicle with this serial number already exists")
    return crud.create_vehicle(db=db, vehicle=vehicle)

@router.get("/", response_model=List[schemas.Vehicle])
def read_vehicles(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get all vehicles with pagination"""
    vehicles = crud.get_vehicles(db, skip=skip, limit=limit)
    return vehicles

@router.get("/{vehicle_id}", response_model=schemas.VehicleComplete)
def read_vehicle(
    vehicle_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific vehicle with all related data"""
    db_vehicle = crud.get_vehicle(db, vehicle_id=vehicle_id)
    if db_vehicle is None:
        raise HTTPException(status_code=404, detail="Vehicle not found")
    return db_vehicle

@router.put("/{vehicle_id}", response_model=schemas.Vehicle)
def update_vehicle(
    vehicle_id: int,
    vehicle: schemas.VehicleUpdate,
    db: Session = Depends(get_db)
):
    """Update a vehicle"""
    db_vehicle = crud.update_vehicle(db, vehicle_id=vehicle_id, vehicle=vehicle)
    if db_vehicle is None:
        raise HTTPException(status_code=404, detail="Vehicle not found")
    return db_vehicle

@router.delete("/{vehicle_id}")
def delete_vehicle(
    vehicle_id: int,
    db: Session = Depends(get_db)
):
    """Delete a vehicle"""
    db_vehicle = crud.delete_vehicle(db, vehicle_id=vehicle_id)
    if db_vehicle is None:
        raise HTTPException(status_code=404, detail="Vehicle not found")
    return {"message": "Vehicle deleted successfully"}

# Maintenance Details endpoints
@router.get("/{vehicle_id}/maintenance-details", response_model=List[schemas.MaintenanceDetail])
def read_vehicle_maintenance_details(
    vehicle_id: int,
    db: Session = Depends(get_db)
):
    """Get all maintenance details for a vehicle"""
    return crud.get_maintenance_details(db, vehicle_id=vehicle_id)

@router.post("/{vehicle_id}/maintenance-details", response_model=schemas.MaintenanceDetail)
def create_vehicle_maintenance_detail(
    vehicle_id: int,
    maintenance_detail: schemas.MaintenanceDetailCreate,
    db: Session = Depends(get_db)
):
    """Create a new maintenance detail for a vehicle"""
    # Verify vehicle exists
    db_vehicle = crud.get_vehicle(db, vehicle_id=vehicle_id)
    if db_vehicle is None:
        raise HTTPException(status_code=404, detail="Vehicle not found")
    
    maintenance_detail.vehicle_id = vehicle_id
    return crud.create_maintenance_detail(db=db, maintenance_detail=maintenance_detail)

# Scheduled Maintenance endpoints
@router.get("/{vehicle_id}/scheduled-maintenance", response_model=List[schemas.ScheduledMaintenance])
def read_vehicle_scheduled_maintenance(
    vehicle_id: int,
    db: Session = Depends(get_db)
):
    """Get all scheduled maintenance for a vehicle"""
    return crud.get_scheduled_maintenance(db, vehicle_id=vehicle_id)

@router.post("/{vehicle_id}/scheduled-maintenance", response_model=schemas.ScheduledMaintenance)
def create_vehicle_scheduled_maintenance(
    vehicle_id: int,
    scheduled_maintenance: schemas.ScheduledMaintenanceCreate,
    db: Session = Depends(get_db)
):
    """Create a new scheduled maintenance for a vehicle"""
    # Verify vehicle exists
    db_vehicle = crud.get_vehicle(db, vehicle_id=vehicle_id)
    if db_vehicle is None:
        raise HTTPException(status_code=404, detail="Vehicle not found")
    
    scheduled_maintenance.vehicle_id = vehicle_id
    return crud.create_scheduled_maintenance(db=db, scheduled_maintenance=scheduled_maintenance)

# Overhaul Schedules endpoints
@router.get("/{vehicle_id}/overhaul-schedules", response_model=List[schemas.OverhaulSchedule])
def read_vehicle_overhaul_schedules(
    vehicle_id: int,
    db: Session = Depends(get_db)
):
    """Get all overhaul schedules for a vehicle"""
    return crud.get_overhaul_schedules(db, vehicle_id=vehicle_id)

@router.post("/{vehicle_id}/overhaul-schedules", response_model=schemas.OverhaulSchedule)
def create_vehicle_overhaul_schedule(
    vehicle_id: int,
    overhaul_schedule: schemas.OverhaulScheduleCreate,
    db: Session = Depends(get_db)
):
    """Create a new overhaul schedule for a vehicle"""
    # Verify vehicle exists
    db_vehicle = crud.get_vehicle(db, vehicle_id=vehicle_id)
    if db_vehicle is None:
        raise HTTPException(status_code=404, detail="Vehicle not found")
    
    overhaul_schedule.vehicle_id = vehicle_id
    return crud.create_overhaul_schedule(db=db, overhaul_schedule=overhaul_schedule)

# Repair Records endpoints
@router.get("/{vehicle_id}/repair-records", response_model=List[schemas.RepairRecord])
def read_vehicle_repair_records(
    vehicle_id: int,
    db: Session = Depends(get_db)
):
    """Get all repair records for a vehicle"""
    return crud.get_repair_records(db, vehicle_id=vehicle_id)

@router.post("/{vehicle_id}/repair-records", response_model=schemas.RepairRecord)
def create_vehicle_repair_record(
    vehicle_id: int,
    repair_record: schemas.RepairRecordCreate,
    db: Session = Depends(get_db)
):
    """Create a new repair record for a vehicle"""
    # Verify vehicle exists
    db_vehicle = crud.get_vehicle(db, vehicle_id=vehicle_id)
    if db_vehicle is None:
        raise HTTPException(status_code=404, detail="Vehicle not found")
    
    repair_record.vehicle_id = vehicle_id
    return crud.create_repair_record(db=db, repair_record=repair_record)
