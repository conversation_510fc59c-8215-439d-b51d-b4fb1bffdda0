from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from app import crud, models, schemas
from app.database import get_db

router = APIRouter(
    prefix="/maintenance",
    tags=["maintenance"],
    responses={404: {"description": "Not found"}},
)

# Maintenance Details endpoints
@router.get("/details/{detail_id}", response_model=schemas.MaintenanceDetail)
def read_maintenance_detail(
    detail_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific maintenance detail"""
    db_detail = db.query(models.MaintenanceDetail).filter(models.MaintenanceDetail.id == detail_id).first()
    if db_detail is None:
        raise HTTPException(status_code=404, detail="Maintenance detail not found")
    return db_detail

@router.put("/details/{detail_id}", response_model=schemas.MaintenanceDetail)
def update_maintenance_detail(
    detail_id: int,
    maintenance_detail: schemas.MaintenanceDetailUpdate,
    db: Session = Depends(get_db)
):
    """Update a maintenance detail"""
    db_detail = crud.update_maintenance_detail(db, detail_id=detail_id, maintenance_detail=maintenance_detail)
    if db_detail is None:
        raise HTTPException(status_code=404, detail="Maintenance detail not found")
    return db_detail

@router.delete("/details/{detail_id}")
def delete_maintenance_detail(
    detail_id: int,
    db: Session = Depends(get_db)
):
    """Delete a maintenance detail"""
    db_detail = crud.delete_maintenance_detail(db, detail_id=detail_id)
    if db_detail is None:
        raise HTTPException(status_code=404, detail="Maintenance detail not found")
    return {"message": "Maintenance detail deleted successfully"}

# Scheduled Maintenance endpoints
@router.get("/scheduled/{maintenance_id}", response_model=schemas.ScheduledMaintenance)
def read_scheduled_maintenance(
    maintenance_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific scheduled maintenance"""
    db_maintenance = db.query(models.ScheduledMaintenance).filter(models.ScheduledMaintenance.id == maintenance_id).first()
    if db_maintenance is None:
        raise HTTPException(status_code=404, detail="Scheduled maintenance not found")
    return db_maintenance

@router.put("/scheduled/{maintenance_id}", response_model=schemas.ScheduledMaintenance)
def update_scheduled_maintenance(
    maintenance_id: int,
    scheduled_maintenance: schemas.ScheduledMaintenanceUpdate,
    db: Session = Depends(get_db)
):
    """Update a scheduled maintenance"""
    db_maintenance = crud.update_scheduled_maintenance(db, maintenance_id=maintenance_id, scheduled_maintenance=scheduled_maintenance)
    if db_maintenance is None:
        raise HTTPException(status_code=404, detail="Scheduled maintenance not found")
    return db_maintenance

@router.delete("/scheduled/{maintenance_id}")
def delete_scheduled_maintenance(
    maintenance_id: int,
    db: Session = Depends(get_db)
):
    """Delete a scheduled maintenance"""
    db_maintenance = crud.delete_scheduled_maintenance(db, maintenance_id=maintenance_id)
    if db_maintenance is None:
        raise HTTPException(status_code=404, detail="Scheduled maintenance not found")
    return {"message": "Scheduled maintenance deleted successfully"}

# Overhaul Schedules endpoints
@router.get("/overhaul/{schedule_id}", response_model=schemas.OverhaulSchedule)
def read_overhaul_schedule(
    schedule_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific overhaul schedule"""
    db_schedule = db.query(models.OverhaulSchedule).filter(models.OverhaulSchedule.id == schedule_id).first()
    if db_schedule is None:
        raise HTTPException(status_code=404, detail="Overhaul schedule not found")
    return db_schedule

@router.put("/overhaul/{schedule_id}", response_model=schemas.OverhaulSchedule)
def update_overhaul_schedule(
    schedule_id: int,
    overhaul_schedule: schemas.OverhaulScheduleUpdate,
    db: Session = Depends(get_db)
):
    """Update an overhaul schedule"""
    db_schedule = crud.update_overhaul_schedule(db, schedule_id=schedule_id, overhaul_schedule=overhaul_schedule)
    if db_schedule is None:
        raise HTTPException(status_code=404, detail="Overhaul schedule not found")
    return db_schedule

@router.delete("/overhaul/{schedule_id}")
def delete_overhaul_schedule(
    schedule_id: int,
    db: Session = Depends(get_db)
):
    """Delete an overhaul schedule"""
    db_schedule = crud.delete_overhaul_schedule(db, schedule_id=schedule_id)
    if db_schedule is None:
        raise HTTPException(status_code=404, detail="Overhaul schedule not found")
    return {"message": "Overhaul schedule deleted successfully"}

# Repair Records endpoints
@router.get("/repair/{record_id}", response_model=schemas.RepairRecord)
def read_repair_record(
    record_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific repair record"""
    db_record = db.query(models.RepairRecord).filter(models.RepairRecord.id == record_id).first()
    if db_record is None:
        raise HTTPException(status_code=404, detail="Repair record not found")
    return db_record

@router.put("/repair/{record_id}", response_model=schemas.RepairRecord)
def update_repair_record(
    record_id: int,
    repair_record: schemas.RepairRecordUpdate,
    db: Session = Depends(get_db)
):
    """Update a repair record"""
    db_record = crud.update_repair_record(db, record_id=record_id, repair_record=repair_record)
    if db_record is None:
        raise HTTPException(status_code=404, detail="Repair record not found")
    return db_record

@router.delete("/repair/{record_id}")
def delete_repair_record(
    record_id: int,
    db: Session = Depends(get_db)
):
    """Delete a repair record"""
    db_record = crud.delete_repair_record(db, record_id=record_id)
    if db_record is None:
        raise HTTPException(status_code=404, detail="Repair record not found")
    return {"message": "Repair record deleted successfully"}
