import React from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useQuery } from 'react-query'
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material'
import { ArrowBack } from '@mui/icons-material'
import axios from 'axios'

const fetchVehicle = async (id) => {
  const response = await axios.get(`/api/v1/vehicles/${id}`)
  return response.data
}

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`vehicle-tabpanel-${index}`}
      aria-labelledby={`vehicle-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  )
}

function VehicleDetail() {
  const { id } = useParams()
  const navigate = useNavigate()
  const [tabValue, setTabValue] = React.useState(0)

  const { data: vehicle, isLoading, error } = useQuery(
    ['vehicle', id],
    () => fetchVehicle(id)
  )

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue)
  }

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    )
  }

  if (error) {
    return (
      <Alert severity="error">
        Error loading vehicle details: {error.message}
      </Alert>
    )
  }

  return (
    <Box>
      <Box display="flex" alignItems="center" mb={3}>
        <Button
          startIcon={<ArrowBack />}
          onClick={() => navigate('/vehicles')}
          sx={{ mr: 2 }}
        >
          Back to Vehicles
        </Button>
        <Typography variant="h4" component="h1">
          Vehicle Details - {vehicle?.ser_no}
        </Typography>
      </Box>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Basic Information
              </Typography>
              <Typography><strong>Serial Number:</strong> {vehicle?.ser_no || 'N/A'}</Typography>
              <Typography><strong>BA Number:</strong> {vehicle?.ba_no || 'N/A'}</Typography>
              <Typography><strong>Make/Type:</strong> {vehicle?.make_type || 'N/A'}</Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Usage Information
              </Typography>
              <Typography><strong>KM Run:</strong> {vehicle?.km_run ? `${vehicle.km_run.toLocaleString()} km` : 'N/A'}</Typography>
              <Typography><strong>Hours Run:</strong> {vehicle?.hrs_run ? `${vehicle.hrs_run.toLocaleString()} hrs` : 'N/A'}</Typography>
              <Typography><strong>Date of Release:</strong> {vehicle?.date_of_rel ? new Date(vehicle.date_of_rel).toLocaleDateString() : 'N/A'}</Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label="Maintenance Details" />
          <Tab label="Scheduled Maintenance" />
          <Tab label="Overhaul Schedules" />
          <Tab label="Repair Records" />
          <Tab label="Tire Information" />
          <Tab label="Battery Information" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Typography variant="h6" gutterBottom>
          Maintenance Details
        </Typography>
        {vehicle?.maintenance_details?.length > 0 ? (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Type</TableCell>
                  <TableCell>Sub Component</TableCell>
                  <TableCell>Capacity</TableCell>
                  <TableCell>Grade</TableCell>
                  <TableCell>Last Change</TableCell>
                  <TableCell>Periodicity</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {vehicle.maintenance_details.map((detail) => (
                  <TableRow key={detail.id}>
                    <TableCell>{detail.maintenance_type || 'N/A'}</TableCell>
                    <TableCell>{detail.sub_component || 'N/A'}</TableCell>
                    <TableCell>{detail.capacity_ltrs_kg || 'N/A'}</TableCell>
                    <TableCell>{detail.grade || 'N/A'}</TableCell>
                    <TableCell>{detail.dt_of_last_change ? new Date(detail.dt_of_last_change).toLocaleDateString() : 'N/A'}</TableCell>
                    <TableCell>{detail.periodicity_km_hrs_month || 'N/A'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          <Typography color="textSecondary">No maintenance details available</Typography>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Typography variant="h6" gutterBottom>
          Scheduled Maintenance
        </Typography>
        {vehicle?.scheduled_maintenance?.length > 0 ? (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Type</TableCell>
                  <TableCell>Done Date</TableCell>
                  <TableCell>Due Date</TableCell>
                  <TableCell>Status</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {vehicle.scheduled_maintenance.map((maintenance) => (
                  <TableRow key={maintenance.id}>
                    <TableCell>{maintenance.maintenance_type || 'N/A'}</TableCell>
                    <TableCell>{maintenance.done_date ? new Date(maintenance.done_date).toLocaleDateString() : 'N/A'}</TableCell>
                    <TableCell>{maintenance.due_date ? new Date(maintenance.due_date).toLocaleDateString() : 'N/A'}</TableCell>
                    <TableCell>
                      {maintenance.due_date && new Date(maintenance.due_date) < new Date() ? (
                        <Chip label="Overdue" color="error" size="small" />
                      ) : (
                        <Chip label="On Schedule" color="success" size="small" />
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          <Typography color="textSecondary">No scheduled maintenance available</Typography>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Typography variant="h6" gutterBottom>
          Overhaul Schedules
        </Typography>
        {vehicle?.overhaul_schedules?.length > 0 ? (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Type</TableCell>
                  <TableCell>Done Date</TableCell>
                  <TableCell>Due Date</TableCell>
                  <TableCell>Status</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {vehicle.overhaul_schedules.map((overhaul) => (
                  <TableRow key={overhaul.id}>
                    <TableCell>{overhaul.overhaul_type || 'N/A'}</TableCell>
                    <TableCell>{overhaul.done_date ? new Date(overhaul.done_date).toLocaleDateString() : 'N/A'}</TableCell>
                    <TableCell>{overhaul.due_date ? new Date(overhaul.due_date).toLocaleDateString() : 'N/A'}</TableCell>
                    <TableCell>
                      {overhaul.due_date && new Date(overhaul.due_date) < new Date() ? (
                        <Chip label="Overdue" color="error" size="small" />
                      ) : (
                        <Chip label="On Schedule" color="success" size="small" />
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          <Typography color="textSecondary">No overhaul schedules available</Typography>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <Typography variant="h6" gutterBottom>
          Repair Records
        </Typography>
        {vehicle?.repair_records?.length > 0 ? (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Type</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Description</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {vehicle.repair_records.map((repair) => (
                  <TableRow key={repair.id}>
                    <TableCell>{repair.repair_type || 'N/A'}</TableCell>
                    <TableCell>{repair.repair_date ? new Date(repair.repair_date).toLocaleDateString() : 'N/A'}</TableCell>
                    <TableCell>{repair.description || 'N/A'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          <Typography color="textSecondary">No repair records available</Typography>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={4}>
        <Typography variant="h6" gutterBottom>
          Tire Information
        </Typography>
        {vehicle?.tyre_information?.length > 0 ? (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Date of Change</TableCell>
                  <TableCell>Rotation</TableCell>
                  <TableCell>Condition</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {vehicle.tyre_information.map((tire) => (
                  <TableRow key={tire.id}>
                    <TableCell>{tire.dt_of_change ? new Date(tire.dt_of_change).toLocaleDateString() : 'N/A'}</TableCell>
                    <TableCell>{tire.rotation || 'N/A'}</TableCell>
                    <TableCell>{tire.condition_kms_yrs || 'N/A'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          <Typography color="textSecondary">No tire information available</Typography>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={5}>
        <Typography variant="h6" gutterBottom>
          Battery Information
        </Typography>
        {vehicle?.battery_information?.length > 0 ? (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Date of Change</TableCell>
                  <TableCell>Life</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {vehicle.battery_information.map((battery) => (
                  <TableRow key={battery.id}>
                    <TableCell>{battery.dt_of_change ? new Date(battery.dt_of_change).toLocaleDateString() : 'N/A'}</TableCell>
                    <TableCell>{battery.life || 'N/A'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          <Typography color="textSecondary">No battery information available</Typography>
        )}
      </TabPanel>
    </Box>
  )
}

export default VehicleDetail
