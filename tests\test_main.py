import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.main import app
from app.database import get_db, Base
from app import models

# Create test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture(scope="module")
def client():
    # Create test database tables
    Base.metadata.create_all(bind=engine)
    with TestClient(app) as c:
        yield c
    # Clean up
    Base.metadata.drop_all(bind=engine)

def test_read_root(client):
    """Test root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data

def test_health_check(client):
    """Test health check endpoint"""
    response = client.get("/api/v1/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["database"] == "connected"

def test_get_stats(client):
    """Test stats endpoint"""
    response = client.get("/api/v1/stats")
    assert response.status_code == 200
    data = response.json()
    assert "vehicles" in data
    assert "maintenance_details" in data
    assert "scheduled_maintenance" in data

def test_create_vehicle(client):
    """Test creating a vehicle"""
    vehicle_data = {
        "ser_no": "TEST001",
        "ba_no": "BA001",
        "make_type": "Test Vehicle",
        "km_run": 1000.0,
        "hrs_run": 100.0
    }
    
    response = client.post("/api/v1/vehicles/", json=vehicle_data)
    assert response.status_code == 200
    data = response.json()
    assert data["ser_no"] == "TEST001"
    assert data["ba_no"] == "BA001"
    assert data["make_type"] == "Test Vehicle"

def test_get_vehicles(client):
    """Test getting all vehicles"""
    response = client.get("/api/v1/vehicles/")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)

def test_get_vehicle_by_id(client):
    """Test getting a specific vehicle"""
    # First create a vehicle
    vehicle_data = {
        "ser_no": "TEST002",
        "ba_no": "BA002",
        "make_type": "Test Vehicle 2"
    }
    
    create_response = client.post("/api/v1/vehicles/", json=vehicle_data)
    assert create_response.status_code == 200
    vehicle_id = create_response.json()["id"]
    
    # Then get it
    response = client.get(f"/api/v1/vehicles/{vehicle_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["ser_no"] == "TEST002"

def test_update_vehicle(client):
    """Test updating a vehicle"""
    # First create a vehicle
    vehicle_data = {
        "ser_no": "TEST003",
        "ba_no": "BA003",
        "make_type": "Test Vehicle 3"
    }
    
    create_response = client.post("/api/v1/vehicles/", json=vehicle_data)
    assert create_response.status_code == 200
    vehicle_id = create_response.json()["id"]
    
    # Then update it
    update_data = {
        "km_run": 2000.0,
        "hrs_run": 200.0
    }
    
    response = client.put(f"/api/v1/vehicles/{vehicle_id}", json=update_data)
    assert response.status_code == 200
    data = response.json()
    assert data["km_run"] == 2000.0
    assert data["hrs_run"] == 200.0

def test_create_maintenance_detail(client):
    """Test creating a maintenance detail"""
    # First create a vehicle
    vehicle_data = {
        "ser_no": "TEST004",
        "ba_no": "BA004",
        "make_type": "Test Vehicle 4"
    }
    
    create_response = client.post("/api/v1/vehicles/", json=vehicle_data)
    assert create_response.status_code == 200
    vehicle_id = create_response.json()["id"]
    
    # Then create maintenance detail
    maintenance_data = {
        "maintenance_type": "ENG OIL",
        "grade": "15W-40",
        "capacity_ltrs_kg": 5.0
    }
    
    response = client.post(f"/api/v1/vehicles/{vehicle_id}/maintenance-details", json=maintenance_data)
    assert response.status_code == 200
    data = response.json()
    assert data["maintenance_type"] == "ENG OIL"
    assert data["grade"] == "15W-40"
    assert data["vehicle_id"] == vehicle_id

def test_get_maintenance_details(client):
    """Test getting maintenance details for a vehicle"""
    # First create a vehicle
    vehicle_data = {
        "ser_no": "TEST005",
        "ba_no": "BA005",
        "make_type": "Test Vehicle 5"
    }
    
    create_response = client.post("/api/v1/vehicles/", json=vehicle_data)
    assert create_response.status_code == 200
    vehicle_id = create_response.json()["id"]
    
    # Get maintenance details (should be empty initially)
    response = client.get(f"/api/v1/vehicles/{vehicle_id}/maintenance-details")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
