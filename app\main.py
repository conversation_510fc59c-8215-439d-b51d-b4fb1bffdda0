from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from app.database import engine, get_db
from app import models
from app.routers import vehicles, maintenance

# Create database tables
models.Base.metadata.create_all(bind=engine)

# Create FastAPI app
app = FastAPI(
    title="Vehicle Maintenance Management System",
    description="A comprehensive system for managing vehicle maintenance, repairs, and schedules",
    version="1.0.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(vehicles.router, prefix="/api/v1")
app.include_router(maintenance.router, prefix="/api/v1")

@app.get("/")
def read_root():
    """Root endpoint"""
    return {
        "message": "Welcome to Vehicle Maintenance Management System",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }

@app.get("/api/v1/health")
def health_check(db: Session = Depends(get_db)):
    """Health check endpoint"""
    try:
        # Test database connection
        from sqlalchemy import text
        db.execute(text("SELECT 1"))
        return {"status": "healthy", "database": "connected"}
    except Exception as e:
        return {"status": "unhealthy", "database": "disconnected", "error": str(e)}

@app.get("/api/v1/stats")
def get_stats(db: Session = Depends(get_db)):
    """Get system statistics"""
    try:
        vehicle_count = db.query(models.Vehicle).count()
        maintenance_count = db.query(models.MaintenanceDetail).count()
        scheduled_maintenance_count = db.query(models.ScheduledMaintenance).count()
        overhaul_count = db.query(models.OverhaulSchedule).count()
        repair_count = db.query(models.RepairRecord).count()
        
        return {
            "vehicles": vehicle_count,
            "maintenance_details": maintenance_count,
            "scheduled_maintenance": scheduled_maintenance_count,
            "overhaul_schedules": overhaul_count,
            "repair_records": repair_count
        }
    except Exception as e:
        return {"error": str(e)}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
