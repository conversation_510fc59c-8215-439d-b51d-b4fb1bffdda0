from sqlalchemy import Column, Integer, String, Float, Date, Text, ForeignKey
from sqlalchemy.orm import relationship
from app.database import Base

class Vehicle(Base):
    __tablename__ = "vehicles"
    
    id = Column(Integer, primary_key=True, index=True)
    ser_no = Column(String, index=True)
    ba_no = Column(String, index=True)
    make_type = Column(String)
    km_run = Column(Float)
    hrs_run = Column(Float)
    date_of_rel = Column(Date)
    
    # Relationships
    maintenance_details = relationship("MaintenanceDetail", back_populates="vehicle")
    scheduled_maintenance = relationship("ScheduledMaintenance", back_populates="vehicle")
    overhaul_schedules = relationship("OverhaulSchedule", back_populates="vehicle")
    discard_criteria = relationship("DiscardCriteria", back_populates="vehicle")
    tyre_information = relationship("TyreInformation", back_populates="vehicle")
    battery_information = relationship("BatteryInformation", back_populates="vehicle")
    repair_records = relationship("RepairRecord", back_populates="vehicle")
    vehicle_attributes = relationship("VehicleAttribute", back_populates="vehicle")

class MaintenanceDetail(Base):
    __tablename__ = "maintenance_details"
    
    id = Column(Integer, primary_key=True, index=True)
    vehicle_id = Column(Integer, ForeignKey("vehicles.id"))
    maintenance_type = Column(String)  # e.g., ENG OIL, TXN OIL
    sub_component = Column(String)     # e.g., GEAR BOX, AUX GEAR BOX
    capacity_ltrs_kg = Column(Float)
    addl_10_percent_top_up = Column(Float)
    grade = Column(String)
    dt_of_last_change = Column(Date)
    periodicity_km_hrs_month = Column(String)
    
    # Relationship
    vehicle = relationship("Vehicle", back_populates="maintenance_details")

class ScheduledMaintenance(Base):
    __tablename__ = "scheduled_maintenance"
    
    id = Column(Integer, primary_key=True, index=True)
    vehicle_id = Column(Integer, ForeignKey("vehicles.id"))
    maintenance_type = Column(String)  # e.g., TM - I, TM - II
    done_date = Column(Date)
    due_date = Column(Date)
    
    # Relationship
    vehicle = relationship("Vehicle", back_populates="scheduled_maintenance")

class OverhaulSchedule(Base):
    __tablename__ = "overhaul_schedules"
    
    id = Column(Integer, primary_key=True, index=True)
    vehicle_id = Column(Integer, ForeignKey("vehicles.id"))
    overhaul_type = Column(String)  # e.g., OH-I, OH-II
    done_date = Column(Date)
    due_date = Column(Date)
    
    # Relationship
    vehicle = relationship("Vehicle", back_populates="overhaul_schedules")

class DiscardCriteria(Base):
    __tablename__ = "discard_criteria"
    
    id = Column(Integer, primary_key=True, index=True)
    vehicle_id = Column(Integer, ForeignKey("vehicles.id"))
    vintage_years = Column(Integer)
    meterage_kms = Column(Float)
    
    # Relationship
    vehicle = relationship("Vehicle", back_populates="discard_criteria")

class TyreInformation(Base):
    __tablename__ = "tyre_information"
    
    id = Column(Integer, primary_key=True, index=True)
    vehicle_id = Column(Integer, ForeignKey("vehicles.id"))
    dt_of_change = Column(Date)
    rotation = Column(String)
    condition_kms_yrs = Column(String)
    
    # Relationship
    vehicle = relationship("Vehicle", back_populates="tyre_information")

class BatteryInformation(Base):
    __tablename__ = "battery_information"
    
    id = Column(Integer, primary_key=True, index=True)
    vehicle_id = Column(Integer, ForeignKey("vehicles.id"))
    dt_of_change = Column(Date)
    life = Column(String)
    
    # Relationship
    vehicle = relationship("Vehicle", back_populates="battery_information")

class RepairRecord(Base):
    __tablename__ = "repair_records"
    
    id = Column(Integer, primary_key=True, index=True)
    vehicle_id = Column(Integer, ForeignKey("vehicles.id"))
    repair_type = Column(String)  # e.g., major, miscellaneous
    repair_date = Column(Date)
    description = Column(Text)
    
    # Relationship
    vehicle = relationship("Vehicle", back_populates="repair_records")

class VehicleAttribute(Base):
    __tablename__ = "vehicle_attributes"
    
    id = Column(Integer, primary_key=True, index=True)
    vehicle_id = Column(Integer, ForeignKey("vehicles.id"))
    attribute_name = Column(String)
    attribute_value = Column(String)
    
    # Relationship
    vehicle = relationship("Vehicle", back_populates="vehicle_attributes")
