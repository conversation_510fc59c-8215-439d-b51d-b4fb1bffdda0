from sqlalchemy.orm import Session
from typing import List, Optional
from app import models, schemas

# Vehicle CRUD operations
def get_vehicle(db: Session, vehicle_id: int):
    return db.query(models.Vehicle).filter(models.Vehicle.id == vehicle_id).first()

def get_vehicle_by_ser_no(db: Session, ser_no: str):
    return db.query(models.Vehicle).filter(models.Vehicle.ser_no == ser_no).first()

def get_vehicles(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.Vehicle).offset(skip).limit(limit).all()

def create_vehicle(db: Session, vehicle: schemas.VehicleCreate):
    db_vehicle = models.Vehicle(**vehicle.dict())
    db.add(db_vehicle)
    db.commit()
    db.refresh(db_vehicle)
    return db_vehicle

def update_vehicle(db: Session, vehicle_id: int, vehicle: schemas.VehicleUpdate):
    db_vehicle = db.query(models.Vehicle).filter(models.Vehicle.id == vehicle_id).first()
    if db_vehicle:
        update_data = vehicle.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_vehicle, field, value)
        db.commit()
        db.refresh(db_vehicle)
    return db_vehicle

def delete_vehicle(db: Session, vehicle_id: int):
    db_vehicle = db.query(models.Vehicle).filter(models.Vehicle.id == vehicle_id).first()
    if db_vehicle:
        db.delete(db_vehicle)
        db.commit()
    return db_vehicle

# Maintenance Detail CRUD operations
def get_maintenance_details(db: Session, vehicle_id: int):
    return db.query(models.MaintenanceDetail).filter(models.MaintenanceDetail.vehicle_id == vehicle_id).all()

def create_maintenance_detail(db: Session, maintenance_detail: schemas.MaintenanceDetailCreate):
    db_maintenance_detail = models.MaintenanceDetail(**maintenance_detail.dict())
    db.add(db_maintenance_detail)
    db.commit()
    db.refresh(db_maintenance_detail)
    return db_maintenance_detail

def update_maintenance_detail(db: Session, detail_id: int, maintenance_detail: schemas.MaintenanceDetailUpdate):
    db_detail = db.query(models.MaintenanceDetail).filter(models.MaintenanceDetail.id == detail_id).first()
    if db_detail:
        update_data = maintenance_detail.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_detail, field, value)
        db.commit()
        db.refresh(db_detail)
    return db_detail

def delete_maintenance_detail(db: Session, detail_id: int):
    db_detail = db.query(models.MaintenanceDetail).filter(models.MaintenanceDetail.id == detail_id).first()
    if db_detail:
        db.delete(db_detail)
        db.commit()
    return db_detail

# Scheduled Maintenance CRUD operations
def get_scheduled_maintenance(db: Session, vehicle_id: int):
    return db.query(models.ScheduledMaintenance).filter(models.ScheduledMaintenance.vehicle_id == vehicle_id).all()

def create_scheduled_maintenance(db: Session, scheduled_maintenance: schemas.ScheduledMaintenanceCreate):
    db_scheduled_maintenance = models.ScheduledMaintenance(**scheduled_maintenance.dict())
    db.add(db_scheduled_maintenance)
    db.commit()
    db.refresh(db_scheduled_maintenance)
    return db_scheduled_maintenance

def update_scheduled_maintenance(db: Session, maintenance_id: int, scheduled_maintenance: schemas.ScheduledMaintenanceUpdate):
    db_maintenance = db.query(models.ScheduledMaintenance).filter(models.ScheduledMaintenance.id == maintenance_id).first()
    if db_maintenance:
        update_data = scheduled_maintenance.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_maintenance, field, value)
        db.commit()
        db.refresh(db_maintenance)
    return db_maintenance

def delete_scheduled_maintenance(db: Session, maintenance_id: int):
    db_maintenance = db.query(models.ScheduledMaintenance).filter(models.ScheduledMaintenance.id == maintenance_id).first()
    if db_maintenance:
        db.delete(db_maintenance)
        db.commit()
    return db_maintenance

# Overhaul Schedule CRUD operations
def get_overhaul_schedules(db: Session, vehicle_id: int):
    return db.query(models.OverhaulSchedule).filter(models.OverhaulSchedule.vehicle_id == vehicle_id).all()

def create_overhaul_schedule(db: Session, overhaul_schedule: schemas.OverhaulScheduleCreate):
    db_overhaul_schedule = models.OverhaulSchedule(**overhaul_schedule.dict())
    db.add(db_overhaul_schedule)
    db.commit()
    db.refresh(db_overhaul_schedule)
    return db_overhaul_schedule

def update_overhaul_schedule(db: Session, schedule_id: int, overhaul_schedule: schemas.OverhaulScheduleUpdate):
    db_schedule = db.query(models.OverhaulSchedule).filter(models.OverhaulSchedule.id == schedule_id).first()
    if db_schedule:
        update_data = overhaul_schedule.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_schedule, field, value)
        db.commit()
        db.refresh(db_schedule)
    return db_schedule

def delete_overhaul_schedule(db: Session, schedule_id: int):
    db_schedule = db.query(models.OverhaulSchedule).filter(models.OverhaulSchedule.id == schedule_id).first()
    if db_schedule:
        db.delete(db_schedule)
        db.commit()
    return db_schedule

# Repair Record CRUD operations
def get_repair_records(db: Session, vehicle_id: int):
    return db.query(models.RepairRecord).filter(models.RepairRecord.vehicle_id == vehicle_id).all()

def create_repair_record(db: Session, repair_record: schemas.RepairRecordCreate):
    db_repair_record = models.RepairRecord(**repair_record.dict())
    db.add(db_repair_record)
    db.commit()
    db.refresh(db_repair_record)
    return db_repair_record

def update_repair_record(db: Session, record_id: int, repair_record: schemas.RepairRecordUpdate):
    db_record = db.query(models.RepairRecord).filter(models.RepairRecord.id == record_id).first()
    if db_record:
        update_data = repair_record.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_record, field, value)
        db.commit()
        db.refresh(db_record)
    return db_record

def delete_repair_record(db: Session, record_id: int):
    db_record = db.query(models.RepairRecord).filter(models.RepairRecord.id == record_id).first()
    if db_record:
        db.delete(db_record)
        db.commit()
    return db_record

# Tyre Information CRUD operations
def get_tyre_information(db: Session, vehicle_id: int):
    return db.query(models.TyreInformation).filter(models.TyreInformation.vehicle_id == vehicle_id).all()

def create_tyre_information(db: Session, tyre_info: schemas.TyreInformationCreate):
    db_tyre_info = models.TyreInformation(**tyre_info.dict())
    db.add(db_tyre_info)
    db.commit()
    db.refresh(db_tyre_info)
    return db_tyre_info

# Battery Information CRUD operations
def get_battery_information(db: Session, vehicle_id: int):
    return db.query(models.BatteryInformation).filter(models.BatteryInformation.vehicle_id == vehicle_id).all()

def create_battery_information(db: Session, battery_info: schemas.BatteryInformationCreate):
    db_battery_info = models.BatteryInformation(**battery_info.dict())
    db.add(db_battery_info)
    db.commit()
    db.refresh(db_battery_info)
    return db_battery_info

# Vehicle Attributes CRUD operations
def get_vehicle_attributes(db: Session, vehicle_id: int):
    return db.query(models.VehicleAttribute).filter(models.VehicleAttribute.vehicle_id == vehicle_id).all()

def create_vehicle_attribute(db: Session, vehicle_attr: schemas.VehicleAttributeCreate):
    db_vehicle_attr = models.VehicleAttribute(**vehicle_attr.dict())
    db.add(db_vehicle_attr)
    db.commit()
    db.refresh(db_vehicle_attr)
    return db_vehicle_attr
