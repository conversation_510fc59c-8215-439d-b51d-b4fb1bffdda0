import pandas as pd
import sqlite3
import re

def clean_column_name(col):
    """Clean column names by replacing non-alphanumeric characters with underscores."""
    return re.sub(r'\W+', '_', col)

def generate_create_table(df, table_name):
    """Generate SQL CREATE TABLE statement based on DataFrame columns and types."""
    columns = []
    for col in df.columns:
        clean_col = clean_column_name(col)
        dtype = df[col].dtype
        if dtype == 'int64':
            sql_type = 'INTEGER'
        elif dtype == 'float64':
            sql_type = 'REAL'
        elif dtype == 'object':
            sql_type = 'TEXT'
        elif dtype == 'datetime64[ns]':
            sql_type = 'TEXT'
        elif dtype == 'bool':
            sql_type = 'INTEGER'
        else:
            sql_type = 'TEXT'
        columns.append(f"{clean_col} {sql_type}")
    columns_str = ', '.join(columns)
    create_table_sql = f"CREATE TABLE IF NOT EXISTS {table_name} ({columns_str});"
    return create_table_sql

# Specify the path to your Excel file
excel_file = r'c:\Users\<USER>\Downloads\SOFTWARE DATA update on 6 Jun 2025.xlsx'

# Read all sheets from the Excel file into a dictionary of DataFrames
sheets = pd.read_excel(excel_file, sheet_name=None)

# Specify the SQLite database file path
db_file = 'inventorycheck.db'

# Connect to the SQLite database
conn = sqlite3.connect(db_file)
cursor = conn.cursor()

# Process each sheet in the Excel file
for sheet_name, df in sheets.items():
    if df.empty:
        print(f"Skipping empty sheet: {sheet_name}")
        continue
    
    # Clean the table name derived from the sheet name
    clean_table_name = clean_column_name(sheet_name)
    
    # Clean all column names in the DataFrame
    df.columns = [clean_column_name(col) for col in df.columns]
    
    # Create the table in SQLite
    create_table_sql = generate_create_table(df, clean_table_name)
    cursor.execute(create_table_sql)
    print(f"Created table {clean_table_name}")
    
    # Insert data into the table
    df.to_sql(clean_table_name, conn, if_exists='append', index=False)
    print(f"Inserted data into {clean_table_name}")

# Commit changes and close the database connection
conn.commit()
conn.close()
print("Database created and data imported successfully.")