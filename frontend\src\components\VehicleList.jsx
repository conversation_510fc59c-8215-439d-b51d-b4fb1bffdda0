import React, { useState } from 'react'
import { useQuery } from 'react-query'
import { useNavigate } from 'react-router-dom'
import {
  Box,
  Typography,
  Button,
  CircularProgress,
  Alert,
  TextField,
  InputAdornment
} from '@mui/material'
import { DataGrid } from '@mui/x-data-grid'
import { Add, Search } from '@mui/icons-material'
import axios from 'axios'

const fetchVehicles = async () => {
  const response = await axios.get('/api/v1/vehicles/')
  return response.data
}

function VehicleList() {
  const navigate = useNavigate()
  const [searchTerm, setSearchTerm] = useState('')
  
  const { data: vehicles, isLoading, error } = useQuery('vehicles', fetchVehicles)

  const columns = [
    { field: 'id', headerName: 'ID', width: 70 },
    { field: 'ser_no', headerName: 'Serial No', width: 130 },
    { field: 'ba_no', headerName: 'BA No', width: 130 },
    { field: 'make_type', headerName: 'Make/Type', width: 200 },
    { 
      field: 'km_run', 
      headerName: 'KM Run', 
      width: 120,
      type: 'number',
      valueFormatter: (params) => params.value ? `${params.value.toLocaleString()} km` : 'N/A'
    },
    { 
      field: 'hrs_run', 
      headerName: 'Hours Run', 
      width: 120,
      type: 'number',
      valueFormatter: (params) => params.value ? `${params.value.toLocaleString()} hrs` : 'N/A'
    },
    { 
      field: 'date_of_rel', 
      headerName: 'Date of Release', 
      width: 150,
      valueFormatter: (params) => params.value ? new Date(params.value).toLocaleDateString() : 'N/A'
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      renderCell: (params) => (
        <Button
          variant="outlined"
          size="small"
          onClick={() => navigate(`/vehicles/${params.row.id}`)}
        >
          View Details
        </Button>
      ),
    },
  ]

  const filteredVehicles = vehicles?.filter(vehicle =>
    vehicle.ser_no?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vehicle.ba_no?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vehicle.make_type?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    )
  }

  if (error) {
    return (
      <Alert severity="error">
        Error loading vehicles: {error.message}
      </Alert>
    )
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Vehicles
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => {/* TODO: Add vehicle dialog */}}
        >
          Add Vehicle
        </Button>
      </Box>

      <Box mb={2}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Search vehicles by serial number, BA number, or make/type..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      <Box style={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={filteredVehicles}
          columns={columns}
          pageSize={10}
          rowsPerPageOptions={[10, 25, 50]}
          disableSelectionOnClick
          autoHeight
        />
      </Box>
    </Box>
  )
}

export default VehicleList
