import React from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { Tabs, Tab, Box } from '@mui/material'
import { Dashboard as DashboardIcon, DirectionsCar, Build } from '@mui/icons-material'

function Navigation() {
  const navigate = useNavigate()
  const location = useLocation()

  const handleChange = (event, newValue) => {
    navigate(newValue)
  }

  return (
    <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
      <Tabs value={location.pathname} onChange={handleChange} aria-label="navigation tabs">
        <Tab 
          icon={<DashboardIcon />} 
          label="Dashboard" 
          value="/" 
        />
        <Tab 
          icon={<DirectionsCar />} 
          label="Vehicles" 
          value="/vehicles" 
        />
      </Tabs>
    </Box>
  )
}

export default Navigation
