#!/usr/bin/env python3
"""
Setup script for Vehicle Maintenance Management System
"""

import subprocess
import sys
import os

def run_command(command, cwd=None):
    """Run a command and return success status"""
    try:
        print(f"Running: {command}")
        result = subprocess.run(command, shell=True, cwd=cwd, check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        return False

def setup_backend():
    """Setup backend dependencies"""
    print("Setting up backend...")
    
    # Install Python dependencies
    if not run_command("pip install -r requirements.txt"):
        return False
    
    # Run data migration if inventorycheck.db exists
    if os.path.exists("inventorycheck.db"):
        print("Found existing database, running migration...")
        if not run_command("python migrate_data.py"):
            print("Warning: Data migration failed, but continuing...")
    
    return True

def setup_frontend():
    """Setup frontend dependencies"""
    print("Setting up frontend...")
    
    # Check if Node.js is installed
    if not run_command("node --version"):
        print("Error: Node.js is not installed. Please install Node.js 16 or higher.")
        return False
    
    # Install frontend dependencies
    if not run_command("npm install", cwd="frontend"):
        return False
    
    return True

def main():
    """Main setup function"""
    print("Vehicle Maintenance Management System Setup")
    print("=" * 50)
    
    # Setup backend
    if not setup_backend():
        print("Backend setup failed!")
        sys.exit(1)
    
    # Setup frontend
    if not setup_frontend():
        print("Frontend setup failed!")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("Setup completed successfully!")
    print("\nTo start the application:")
    print("1. Backend: python run_backend.py")
    print("2. Frontend: cd frontend && npm run dev")
    print("\nThen open http://localhost:3000 in your browser")

if __name__ == "__main__":
    main()
