-- Table to store basic vehicle information
CREATE TABLE vehicles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ser_no TEXT,
    ba_no TEXT,
    make_type TEXT,
    km_run REAL,
    hrs_run REAL,
    date_of_rel DATE
);

-- Table to store maintenance details for various components
CREATE TABLE maintenance_details (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    vehicle_id INTEGER,
    maintenance_type TEXT,  -- e.g., ENG OIL, TXN OIL
    sub_component TEXT,     -- e.g., GEAR BOX, AUX GEAR BOX (NULL if not applicable)
    capacity_ltrs_kg REAL,
    addl_10_percent_top_up REAL,
    grade TEXT,
    dt_of_last_change DATE,
    periodicity_km_hrs_month TEXT,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id)
);

-- Table to store scheduled maintenance tasks (e.g., TM - I, TM - II)
CREATE TABLE scheduled_maintenance (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    vehicle_id INTEGER,
    maintenance_type TEXT,  -- e.g., TM - I, TM - II
    done_date DATE,
    due_date DATE,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id)
);

-- Table to store overhaul schedules (e.g., OH-I, OH-II)
CREATE TABLE overhaul_schedules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    vehicle_id INTEGER,
    overhaul_type TEXT,  -- e.g., OH-I, OH-II
    done_date DATE,
    due_date DATE,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id)
);

-- Table to store discard criteria based on vintage years or meterage
CREATE TABLE discard_criteria (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    vehicle_id INTEGER,
    vintage_years INTEGER,
    meterage_kms REAL,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id)
);

-- Table to store tyre maintenance information
CREATE TABLE tyre_information (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    vehicle_id INTEGER,
    dt_of_change DATE,
    rotation TEXT,
    condition_kms_yrs TEXT,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id)
);

-- Table to store battery maintenance information
CREATE TABLE battery_information (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    vehicle_id INTEGER,
    dt_of_change DATE,
    life TEXT,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id)
);

-- Table to store repair records
CREATE TABLE repair_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    vehicle_id INTEGER,
    repair_type TEXT,  -- e.g., major, miscellaneous
    repair_date DATE,
    description TEXT,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id)
);

-- Table to store additional vehicle-specific attributes
CREATE TABLE vehicle_attributes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    vehicle_id INTEGER,
    attribute_name TEXT,
    attribute_value TEXT,
    FOREIGN KEY (vehicle_id) REFERENCES vehicles(id)
);