# Vehicle Maintenance Management System

A comprehensive web application for managing vehicle maintenance, repairs, and schedules. This system provides a modern interface for tracking vehicle information, maintenance details, scheduled maintenance, overhaul schedules, repair records, and more.

## Features

- **Vehicle Management**: Track vehicle information including serial numbers, BA numbers, make/type, mileage, and hours run
- **Maintenance Tracking**: Record maintenance details for various components (oil changes, filters, etc.)
- **Scheduled Maintenance**: Plan and track scheduled maintenance tasks (TM-I, TM-II)
- **Overhaul Management**: Schedule and monitor major overhauls (OH-I, OH-II)
- **Repair Records**: Maintain detailed repair history
- **Tire & Battery Management**: Track tire rotations and battery replacements
- **Dashboard**: Overview of system statistics and key metrics
- **Search & Filter**: Easy search and filtering capabilities
- **Responsive Design**: Works on desktop and mobile devices

## Technology Stack

### Backend
- **FastAPI**: Modern, fast web framework for building APIs
- **SQLAlchemy**: SQL toolkit and Object-Relational Mapping (ORM)
- **SQLite**: Lightweight database for data storage
- **Pydantic**: Data validation using Python type annotations
- **Uvicorn**: ASGI server for running the application

### Frontend
- **React**: JavaScript library for building user interfaces
- **Material-UI**: React components implementing Google's Material Design
- **React Router**: Declarative routing for React
- **React Query**: Data fetching and caching library
- **Vite**: Fast build tool and development server
- **Axios**: HTTP client for API requests

## Installation and Setup

### Prerequisites
- Python 3.8 or higher
- Node.js 16 or higher
- npm or yarn package manager

### Backend Setup

1. **Clone the repository and navigate to the project directory**
   ```bash
   cd vehicle-maintenance-system
   ```

2. **Create a virtual environment**
   ```bash
   python -m venv venv
   ```

3. **Activate the virtual environment**
   - Windows:
     ```bash
     venv\Scripts\activate
     ```
   - macOS/Linux:
     ```bash
     source venv/bin/activate
     ```

4. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

5. **Run database migration (if you have existing data)**
   ```bash
   python migrate_data.py
   ```

6. **Start the backend server**
   ```bash
   python -m app.main
   ```
   
   Or using uvicorn directly:
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

The backend API will be available at `http://localhost:8000`

### Frontend Setup

1. **Navigate to the frontend directory**
   ```bash
   cd frontend
   ```

2. **Install Node.js dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

The frontend application will be available at `http://localhost:3000`

## API Documentation

Once the backend is running, you can access the interactive API documentation at:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Database Schema

The application uses the following main tables:

- **vehicles**: Basic vehicle information
- **maintenance_details**: Maintenance records for various components
- **scheduled_maintenance**: Scheduled maintenance tasks
- **overhaul_schedules**: Major overhaul schedules
- **discard_criteria**: Vehicle retirement criteria
- **tyre_information**: Tire maintenance records
- **battery_information**: Battery maintenance records
- **repair_records**: Repair history
- **vehicle_attributes**: Additional vehicle-specific attributes

## Usage

### Dashboard
- View system statistics and overview
- Quick access to key metrics

### Vehicle Management
- Add new vehicles to the system
- View list of all vehicles with search and filter capabilities
- View detailed information for each vehicle
- Update vehicle information

### Maintenance Tracking
- Record maintenance activities
- Schedule future maintenance
- Track overhaul schedules
- Maintain repair records

## Development

### Running Tests
```bash
# Backend tests
pytest

# Frontend tests
cd frontend
npm test
```

### Building for Production

#### Backend
The FastAPI application can be deployed using various methods:
- Docker containers
- Traditional server deployment
- Cloud platforms (AWS, GCP, Azure)

#### Frontend
```bash
cd frontend
npm run build
```

This creates a `dist` folder with production-ready files.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please create an issue in the repository or contact the development team.
