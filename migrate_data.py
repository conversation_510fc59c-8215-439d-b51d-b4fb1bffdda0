import sqlite3
import pandas as pd
from datetime import datetime
from app.database import SessionLocal, engine
from app import models
import re

def clean_column_name(col):
    """Clean column names by replacing non-alphanumeric characters with underscores."""
    return re.sub(r'\W+', '_', col)

def parse_date(date_str):
    """Parse date string to datetime object"""
    if pd.isna(date_str) or date_str == '' or date_str is None:
        return None
    
    try:
        # Try different date formats
        for fmt in ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%d-%m-%Y']:
            try:
                return datetime.strptime(str(date_str), fmt).date()
            except ValueError:
                continue
        return None
    except:
        return None

def migrate_existing_data():
    """Migrate data from existing inventorycheck.db to new schema"""
    
    # Create all tables
    models.Base.metadata.create_all(bind=engine)
    
    # Connect to existing database
    old_conn = sqlite3.connect('inventorycheck.db')
    
    # Get all table names from old database
    cursor = old_conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    
    db = SessionLocal()
    
    try:
        for table_name in tables:
            table_name = table_name[0]
            print(f"Processing table: {table_name}")
            
            # Read data from old table
            df = pd.read_sql_query(f"SELECT * FROM `{table_name}`", old_conn)
            
            if df.empty:
                print(f"  - Table {table_name} is empty, skipping...")
                continue
            
            # Process each row as a vehicle
            for index, row in df.iterrows():
                try:
                    # Create vehicle record
                    vehicle_data = {
                        'ser_no': str(row.get('SER_NO', '')),
                        'ba_no': str(row.get('BA_NO', '')),
                        'make_type': table_name,  # Use table name as make/type
                        'km_run': float(row.get('KM_RUN', 0)) if pd.notna(row.get('KM_RUN')) else None,
                        'hrs_run': float(row.get('HRS_RUN', 0)) if pd.notna(row.get('HRS_RUN')) else None,
                        'date_of_rel': parse_date(row.get('DATE_OF_REL'))
                    }
                    
                    # Create vehicle
                    vehicle = models.Vehicle(**vehicle_data)
                    db.add(vehicle)
                    db.flush()  # Get the ID
                    
                    # Process maintenance-related columns
                    maintenance_columns = [col for col in df.columns if any(keyword in col.upper() for keyword in 
                                         ['OIL', 'FILTER', 'COOLANT', 'BRAKE', 'HYDRAULIC', 'GEAR', 'ENGINE'])]
                    
                    for col in maintenance_columns:
                        if pd.notna(row[col]) and str(row[col]).strip() != '':
                            maintenance_detail = models.MaintenanceDetail(
                                vehicle_id=vehicle.id,
                                maintenance_type=col,
                                grade=str(row[col])[:50] if len(str(row[col])) > 50 else str(row[col])
                            )
                            db.add(maintenance_detail)
                    
                    # Process scheduled maintenance columns
                    scheduled_columns = [col for col in df.columns if any(keyword in col.upper() for keyword in 
                                       ['TM', 'MAINTENANCE', 'SERVICE'])]
                    
                    for col in scheduled_columns:
                        if pd.notna(row[col]) and str(row[col]).strip() != '':
                            date_val = parse_date(row[col])
                            if date_val:
                                scheduled_maintenance = models.ScheduledMaintenance(
                                    vehicle_id=vehicle.id,
                                    maintenance_type=col,
                                    done_date=date_val
                                )
                                db.add(scheduled_maintenance)
                    
                    # Process overhaul columns
                    overhaul_columns = [col for col in df.columns if 'OH' in col.upper()]
                    
                    for col in overhaul_columns:
                        if pd.notna(row[col]) and str(row[col]).strip() != '':
                            date_val = parse_date(row[col])
                            if date_val:
                                overhaul_schedule = models.OverhaulSchedule(
                                    vehicle_id=vehicle.id,
                                    overhaul_type=col,
                                    done_date=date_val
                                )
                                db.add(overhaul_schedule)
                    
                    # Process tire-related columns
                    tire_columns = [col for col in df.columns if any(keyword in col.upper() for keyword in 
                                  ['TYRE', 'TIRE', 'WHEEL'])]
                    
                    for col in tire_columns:
                        if pd.notna(row[col]) and str(row[col]).strip() != '':
                            tire_info = models.TyreInformation(
                                vehicle_id=vehicle.id,
                                condition_kms_yrs=str(row[col])[:100] if len(str(row[col])) > 100 else str(row[col])
                            )
                            db.add(tire_info)
                    
                    # Process battery-related columns
                    battery_columns = [col for col in df.columns if 'BATTERY' in col.upper()]
                    
                    for col in battery_columns:
                        if pd.notna(row[col]) and str(row[col]).strip() != '':
                            battery_info = models.BatteryInformation(
                                vehicle_id=vehicle.id,
                                life=str(row[col])[:50] if len(str(row[col])) > 50 else str(row[col])
                            )
                            db.add(battery_info)
                    
                    # Add other columns as vehicle attributes
                    excluded_columns = ['SER_NO', 'BA_NO', 'KM_RUN', 'HRS_RUN', 'DATE_OF_REL']
                    for col in df.columns:
                        if col not in excluded_columns and pd.notna(row[col]) and str(row[col]).strip() != '':
                            vehicle_attr = models.VehicleAttribute(
                                vehicle_id=vehicle.id,
                                attribute_name=col,
                                attribute_value=str(row[col])[:255] if len(str(row[col])) > 255 else str(row[col])
                            )
                            db.add(vehicle_attr)
                    
                    print(f"  - Processed vehicle: {vehicle_data['ser_no']}")
                    
                except Exception as e:
                    print(f"  - Error processing row {index}: {str(e)}")
                    continue
        
        # Commit all changes
        db.commit()
        print("Data migration completed successfully!")
        
    except Exception as e:
        print(f"Error during migration: {str(e)}")
        db.rollback()
    finally:
        db.close()
        old_conn.close()

if __name__ == "__main__":
    migrate_existing_data()
