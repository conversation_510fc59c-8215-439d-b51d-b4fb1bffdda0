import React from 'react'
import { useQuery } from 'react-query'
import { 
  <PERSON>rid, 
  Card, 
  CardContent, 
  Typography, 
  Box,
  CircularProgress,
  Alert
} from '@mui/material'
import { 
  DirectionsCar, 
  Build, 
  Schedule, 
  Engineering,
  Warning
} from '@mui/icons-material'
import axios from 'axios'

const fetchStats = async () => {
  const response = await axios.get('/api/v1/stats')
  return response.data
}

function StatCard({ title, value, icon, color = 'primary' }) {
  return (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="h6">
              {title}
            </Typography>
            <Typography variant="h4" component="h2">
              {value}
            </Typography>
          </Box>
          <Box color={`${color}.main`}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  )
}

function Dashboard() {
  const { data: stats, isLoading, error } = useQuery('stats', fetchStats)

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    )
  }

  if (error) {
    return (
      <Alert severity="error">
        Error loading dashboard data: {error.message}
      </Alert>
    )
  }

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Dashboard
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Vehicles"
            value={stats?.vehicles || 0}
            icon={<DirectionsCar fontSize="large" />}
            color="primary"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Maintenance Records"
            value={stats?.maintenance_details || 0}
            icon={<Build fontSize="large" />}
            color="secondary"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Scheduled Maintenance"
            value={stats?.scheduled_maintenance || 0}
            icon={<Schedule fontSize="large" />}
            color="info"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Overhaul Schedules"
            value={stats?.overhaul_schedules || 0}
            icon={<Engineering fontSize="large" />}
            color="warning"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Repair Records"
            value={stats?.repair_records || 0}
            icon={<Warning fontSize="large" />}
            color="error"
          />
        </Grid>
      </Grid>

      <Box mt={4}>
        <Typography variant="h5" component="h2" gutterBottom>
          System Overview
        </Typography>
        <Card>
          <CardContent>
            <Typography variant="body1" paragraph>
              Welcome to the Vehicle Maintenance Management System. This dashboard provides an overview of your fleet's maintenance status.
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Use the navigation tabs above to manage vehicles, view maintenance schedules, and track repairs.
            </Typography>
          </CardContent>
        </Card>
      </Box>
    </Box>
  )
}

export default Dashboard
